<script setup lang="ts">
import { getList } from '@/api/warehouse'
import type { WarehouseEntity } from '@/api/warehouse/types'
import { saveSystemChannelBindWarehouse } from '@/api/channelEntity'

defineOptions({
  name: 'AddWarehouse'
})

const visible = defineModel({ default: false })

const props = defineProps<{
  systemChannelId?: string
}>()

const emit = defineEmits(['success'])

const formData = ref({
  warehouseName: ''
})

// 已选仓库列表 - 全局保存，不随分页变化
const selectedWarehouses = ref<WarehouseEntity[]>([])

const warehouseList = ref<WarehouseEntity[]>([])
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const loading = ref(false)

// 表格引用，用于设置选中状态
const tableRef = ref()

// 标志：是否正在恢复选中状态，防止触发选择变化事件
const isRestoring = ref(false)

// 获取仓库唯一标识
const getWarehouseKey = (warehouse: WarehouseEntity) => {
  return warehouse.id
}

// 检查仓库是否已选中
const isWarehouseSelected = (warehouse: WarehouseEntity) => {
  const warehouseKey = getWarehouseKey(warehouse)
  return selectedWarehouses.value.some(
    item => getWarehouseKey(item) === warehouseKey
  )
}

// 存储上一次的选中状态，用于计算差异
const lastSelection = ref<WarehouseEntity[]>([])

// 表格多选相关 - 真正的增量更新逻辑
const handleSelectionChange = (val: WarehouseEntity[]) => {
  // 如果正在恢复选中状态，则忽略此次变化
  if (isRestoring.value) {
    // 更新上一次选中状态
    lastSelection.value = [...val]
    return
  }

  // 找出新选中的仓库（在 val 中但不在 lastSelection 中）
  const newlySelected = val.filter(
    item =>
      !lastSelection.value.some(
        last => getWarehouseKey(last) === getWarehouseKey(item)
      )
  )

  // 找出取消选中的仓库（在 lastSelection 中但不在 val 中）
  const deselected = lastSelection.value.filter(
    item =>
      !val.some(current => getWarehouseKey(current) === getWarehouseKey(item))
  )

  // 从全局列表中移除取消选中的仓库
  deselected.forEach(item => {
    const index = selectedWarehouses.value.findIndex(
      selected => getWarehouseKey(selected) === getWarehouseKey(item)
    )
    if (index > -1) {
      selectedWarehouses.value.splice(index, 1)
    }
  })

  // 向全局列表添加新选中的仓库
  newlySelected.forEach(item => {
    // 检查是否已存在，避免重复
    const exists = selectedWarehouses.value.some(
      selected => getWarehouseKey(selected) === getWarehouseKey(item)
    )
    if (!exists) {
      selectedWarehouses.value.push(item)
    }
  })

  // 更新上一次选中状态
  lastSelection.value = [...val]
}

// 设置当前页面的选中状态
const setCurrentPageSelection = () => {
  // 使用 setTimeout 确保表格完全渲染完成
  setTimeout(() => {
    if (!tableRef.value) {
      return
    }

    // 根据全局已选列表找到当前页面需要选中的仓库
    const toSelectWarehouses = warehouseList.value.filter(warehouse =>
      isWarehouseSelected(warehouse)
    )

    if (toSelectWarehouses.length > 0) {
      // 设置恢复标志，防止触发选择变化事件
      isRestoring.value = true

      // 清空当前选中状态
      tableRef.value.clearSelection()

      toSelectWarehouses.forEach(warehouse => {
        tableRef.value.toggleRowSelection(warehouse, true)
      })

      // 验证选中结果并重置标志
      setTimeout(() => {
        const currentSelection = tableRef.value.getSelectionRows()

        // 如果没有成功选中任何仓库，尝试其他方法
        if (currentSelection.length === 0 && toSelectWarehouses.length > 0) {
          // 清除恢复标志，手动触发选择变化
          isRestoring.value = false
          handleSelectionChange(toSelectWarehouses)
        } else {
          // 更新上次选中状态为当前恢复的选中状态
          lastSelection.value = [...currentSelection]
          isRestoring.value = false
        }
      }, 100)
    } else {
      // 确保清空选择
      tableRef.value.clearSelection()
    }
  }, 200) // 增加延迟确保表格渲染完成
}

// 搜索仓库
const handleSearch = () => {
  currentPage.value = 1
  init()
}

const init = async () => {
  try {
    loading.value = true
    // 构建请求参数
    const params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value,
      warehouseName: formData.value.warehouseName || undefined
    }

    const res = await getList(params)
    warehouseList.value = res.result.records as any
    total.value = res.result.total

    // 重置上次选中状态，因为数据已更新
    lastSelection.value = []

    // 数据加载完成后，设置当前页面的选中状态
    setCurrentPageSelection()
  } catch (error: any) {
    ElMessage.error('获取仓库列表失败', error.message)
  } finally {
    loading.value = false
  }
}

const handlePageChange = () => {
  init()
}

const handleOpen = () => {
  // 打开弹窗时清空已选列表
  selectedWarehouses.value = []
  lastSelection.value = []
  formData.value.warehouseName = ''
  init()
}

const handleClose = () => {
  visible.value = false
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  if (selectedWarehouses.value.length === 0) {
    ElMessage.warning('请选择仓库')
    return
  }

  try {
    // 循环调用保存接口
    for (const warehouse of selectedWarehouses.value) {
      await saveSystemChannelBindWarehouse({
        systemChannelId: props.systemChannelId,
        warehouseId: warehouse.id
      })
    }

    ElMessage.success('绑定仓库成功')
    emit('success')
    visible.value = false
  } catch (error: any) {
    ElMessage.error('绑定仓库失败', error.message)
  }
}

// 移除已选仓库
const removeSelectedWarehouse = (index: number) => {
  selectedWarehouses.value.splice(index, 1)
  // 更新当前页面的选中状态
  setCurrentPageSelection()
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="绑定仓库"
    width="70%"
    @open="handleOpen"
    @close="handleClose"
  >
    <div class="flex justify-between mb-16">
      <div class="flex items-center">
        <el-input
          v-model.trim="formData.warehouseName"
          clearable
          placeholder="请输入仓库名称"
        ></el-input>
        <el-button type="primary" class="ml-16" @click="handleSearch">
          搜索
        </el-button>
      </div>

      <div class="flex items-center">
        <el-popover placement="bottom-start" :width="500" trigger="click">
          <template #reference>
            <el-button>已选{{ selectedWarehouses.length }}个仓库</el-button>
          </template>
          <div>
            <el-table
              :data="selectedWarehouses"
              :border="true"
              style="width: 100%"
              max-height="300"
            >
              <el-table-column label="仓库代码" prop="warehouseCode" />
              <el-table-column label="仓库名称" prop="warehouseName" />
              <el-table-column label="操作" width="80">
                <template #default="{ $index }">
                  <el-button
                    type="danger"
                    size="small"
                    text
                    @click="removeSelectedWarehouse($index)"
                  >
                    移除
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-popover>
      </div>
    </div>

    <el-table
      ref="tableRef"
      v-loading="loading"
      :data="warehouseList"
      :border="true"
      style="width: 100%"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="60" />
      <el-table-column label="仓库代码" prop="warehouseCode" />
      <el-table-column label="仓库名称" prop="warehouseName" />
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
      @change="handlePageChange"
    />

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
