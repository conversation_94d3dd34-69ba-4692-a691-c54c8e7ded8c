<script setup lang="ts">
import { useTable } from '@/hooks/useTable'
import {
  findListSystemChannelBindWarehouse,
  changeStatusSystemChannelBindWarehouse
} from '@/api/channelEntity'
import type { SystemChannelBindWarehouseVo } from '@/api/channelEntity/index.d'
import { useToggle } from '@/hooks/useToggle'
import AddWarehouse from './AddWarehouse.vue'

defineOptions({
  name: 'WarehouseTab'
})

// 接收父组件传递的系统渠道ID
const props = defineProps<{
  systemChannelId?: string
}>()

const {
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods
} = useTable<SystemChannelBindWarehouseVo>({
  immediate: true,
  initialFormData: {
    systemChannelId: props.systemChannelId
  },
  fetchDataApi: async () => {
    const res = await findListSystemChannelBindWarehouse({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      systemChannelId: props.systemChannelId
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      // 批量删除
      for (const item of record) {
        await changeStatusSystemChannelBindWarehouse({
          id: item.id,
          status: '0' // 假设0表示删除状态
        })
      }
      return true
    } else {
      // 单个删除
      const res = await changeStatusSystemChannelBindWarehouse({
        id: record.id,
        status: '0'
      })
      return !!res
    }
  }
})

const tableRef = ref()
const [awVisible, handleAw] = useToggle()

// 处理添加仓库成功后的回调
const handleAddWarehouseSuccess = () => {
  tableMethods.handleQuery()
}
</script>

<template>
  <div>
    <div class="mb-18">
      <el-button @click="() => handleAw()">添加仓库</el-button>
    </div>
    <el-table
      v-loading="loading"
      ref="tableRef"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column label="仓库代码" prop="warehouseId" />
      <el-table-column label="仓库名称" prop="systemChannelId" />
      <el-table-column label="结算币种" prop="createTime" width="180" />
      <el-table-column label="状态" prop="status" width="180">
        <template #default="{ row }">
          <el-tag :type="row.status === '1' ? 'success' : 'danger'">
            {{ row.status === '1' ? '启用' : '停用' }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column label="操作" fixed="right" width="260">
        <template #default="{ row }">
          <el-button text type="danger" @click="tableMethods.hadnleDel(row)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />

    <AddWarehouse
      v-model="awVisible"
      :system-channel-id="props.systemChannelId"
      @success="handleAddWarehouseSuccess"
    />
  </div>
</template>

<style lang="scss" scoped></style>
