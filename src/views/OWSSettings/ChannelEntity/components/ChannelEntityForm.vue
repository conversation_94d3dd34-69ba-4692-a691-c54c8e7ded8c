<script setup lang="ts">
defineOptions({
  name: 'ChannelEntityForm'
})

// 定义表单数据类型
interface FormData {
  id?: string
  name?: string
  code?: string
  logisticsProviderId?: string
  apiServiceCode?: string
  carrier?: string
  insuranceServices?: string
  signatureService?: string[]
}

// 保险服务    insurance_service
// 签名服务    signature_service

const carrier_list = inject('carrier_list') as DictItemEntity[]
const insurance_service = inject('insurance_service') as DictItemEntity[]
const signature_service = inject('signature_service') as DictItemEntity[]

// 定义 Props
const props = defineProps<{
  modelValue: FormData
}>()

// 定义 Emits
const emit = defineEmits<{
  'update:modelValue': [value: FormData]
}>()

// 计算属性用于双向绑定
const formData = computed({
  get: () => props.modelValue,
  set: value => emit('update:modelValue', value)
})

// 获取字典数据
// import { useDictItem } from '@/hooks/useDictItem'

// 获取字典数据
// const logisticsProviders = useDictItem('logistics_provider') // 物流商
// const carriers = useDictItem('carrier') // 承运商

// 表单引用
const formDataRef = ref()

// 暴露表单方法给父组件
defineExpose({
  formRef: formDataRef,
  validate: () => formDataRef.value?.validate(),
  resetFields: () => formDataRef.value?.resetFields(),
  clearValidate: () => formDataRef.value?.clearValidate()
})
</script>

<template>
  <el-form
    :model="formData"
    ref="formDataRef"
    label-position="left"
    @submit.prevent
  >
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="物流商账号" prop="logisticsProviderId">
          <!-- :options="logisticsProviders" -->
          <DSelect
            v-model="formData.logisticsProviderId"
            :options="[{ itemText: '1', itemValue: '1' }]"
            placeholder="请选择物流商账号"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流渠道代码" prop="code">
          <el-input
            v-model.trim="formData.code"
            clearable
            placeholder="请输入物流渠道代码"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="物流渠道名称" prop="name">
          <el-input
            v-model.trim="formData.name"
            clearable
            placeholder="请输入物流渠道名称"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="API服务代码" prop="apiServiceCode">
          <el-input
            v-model.trim="formData.apiServiceCode"
            clearable
            placeholder="请输入API服务代码"
          />
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="承运商" prop="carrier">
          <DSelect
            v-model="formData.carrier"
            :options="carrier_list"
            placeholder="请选择"
          ></DSelect>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="保险服务" prop="insuranceServices">
          <!-- <el-radio-group v-model="formData.insuranceServices">
            <el-radio label="支持保险" value="0" />
            <el-radio label="不支持保险" value="1" />
          </el-radio-group> -->

          <DRadioGroup
            v-model="formData.insuranceServices"
            :options="insurance_service"
          ></DRadioGroup>
        </el-form-item>
      </el-col>

      <el-col :span="8">
        <el-form-item label="签名服务" prop="signatureService">
          <el-checkbox-group v-model="formData.signatureService">
            <el-checkbox
              v-for="(item, index) of signature_service"
              :label="item.itemText"
              :value="item.itemValue"
              :key="index"
            />
          </el-checkbox-group>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<style lang="scss" scoped></style>
