<script setup lang="ts">
import {
  saveSystemChannelCondition,
  updateSystemChannelCondition
} from '@/api/channelEntity'
import type { SystemChannelConditionVo } from '@/api/channelEntity/index.d'

defineOptions({
  name: 'AddRules'
})

interface Props {
  editData?: SystemChannelConditionVo | null
}

const props = defineProps<Props>()
const emit = defineEmits<{
  success: []
}>()

const visible = defineModel({ default: false })

// 条件    condition_list
// 条件说明    condition_description
// 条件运算符号    condition_calculation
const condition_list = inject('condition_list') as DictItemEntity[]
// const condition_description = inject(
//   'condition_description'
// ) as DictItemEntity[]
const condition_calculation = inject(
  'condition_calculation'
) as DictItemEntity[]

// 规则数据结构
interface RuleItem {
  logic?: string // 且或关系 (and/or)
  condition: string // 条件
  calculation: string // 运算符
  value: string // 值
  unit?: string // 单位
}

const rules = ref<RuleItem[]>([
  {
    condition: '',
    calculation: '',
    value: '',
    unit: ''
  }
])

const loading = ref(false)

const handleOpen = () => {
  // 如果是编辑模式，初始化数据
  if (props.editData) {
    rules.value = [
      {
        condition: props.editData.condition || '',
        calculation: props.editData.calculation || '',
        value: props.editData.value || '',
        unit: props.editData.unit || ''
      }
    ]
  } else {
    // 新增模式，重置数据
    rules.value = [
      {
        condition: '',
        calculation: '',
        value: '',
        unit: ''
      }
    ]
  }
}

const handleClose = () => {
  visible.value = false
  // 清空数据
  rules.value = [
    {
      condition: '',
      calculation: '',
      value: '',
      unit: ''
    }
  ]
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  try {
    loading.value = true

    // 验证数据
    const validRules = rules.value.filter(
      rule => rule.condition && rule.calculation && rule.value
    )

    if (validRules.length === 0) {
      ElMessage.warning('请至少添加一条有效的条件')
      return
    }

    // 构建请求数据 - 这里简化处理，只取第一条规则
    const ruleData = validRules[0]
    const requestData = {
      condition: ruleData.condition,
      calculation: ruleData.calculation,
      value: ruleData.value,
      unit: ruleData.unit,
      status: '1' // 启用状态
    }

    if (props.editData?.id) {
      // 编辑模式
      await updateSystemChannelCondition({
        ...requestData,
        id: props.editData.id
      })
      ElMessage.success('修改成功')
    } else {
      // 新增模式
      await saveSystemChannelCondition(requestData)
      ElMessage.success('添加成功')
    }

    visible.value = false
    emit('success')
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 逻辑关系选项
const logicOptions = [
  {
    itemText: '且',
    itemValue: 'and'
  },
  {
    itemText: '或',
    itemValue: 'or'
  }
]

// 添加规则
const handleAddRule = () => {
  rules.value.push({
    logic: 'and',
    condition: '',
    calculation: '',
    value: '',
    unit: ''
  })
}

// 删除规则
const handleDeleteRule = (index: number) => {
  if (rules.value.length > 1) {
    rules.value.splice(index, 1)
  }
}
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="props.editData ? '编辑条件' : '添加条件'"
    width="800"
    @open="handleOpen"
    @close="handleClose"
  >
    <div v-loading="loading">
      <div
        class="flex items-center"
        v-for="(item, index) in rules"
        :key="index"
        :class="{ 'mt-16': index !== 0 }"
      >
        <div>
          <div v-if="index === 0" class="!w-70 text-center">如果</div>
          <DSelect
            v-else
            v-model="item.logic"
            :options="logicOptions"
            placeholder="请选择"
            class="!w-70"
          ></DSelect>
        </div>
        <DSelect
          v-model="item.condition"
          :options="condition_list"
          placeholder="请选择条件"
          class="!w-150 ml-8"
        ></DSelect>
        <DSelect
          v-model="item.calculation"
          :options="condition_calculation"
          placeholder="请选择运算符"
          class="!w-150 ml-8"
        ></DSelect>

        <el-input
          v-model="item.value"
          clearable
          placeholder="请输入值"
          class="!w-200 ml-8"
        >
          <template #append>
            <el-input
              v-model="item.unit"
              placeholder="单位"
              style="width: 60px; border: none"
            />
          </template>
        </el-input>

        <el-button
          v-if="index !== 0"
          type="primary"
          link
          class="ml-8"
          @click="handleDeleteRule(index)"
        >
          <div class="i-ep:delete"></div>
        </el-button>
      </div>

      <el-button type="primary" link class="mt-16" @click="handleAddRule">
        <div class="i-ep:plus mr-4"></div>
        添加条件
      </el-button>
    </div>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
