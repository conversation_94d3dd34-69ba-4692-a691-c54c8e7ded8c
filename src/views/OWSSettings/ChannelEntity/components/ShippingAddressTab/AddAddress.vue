<script setup lang="ts">
import {
  saveSystemChannelWarehouse,
  updateSystemChannelWarehouse,
  findInfoSystemChannelWarehouse
} from '@/api/channelEntity'
import type { SystemChannelWarehouseModel } from '@/api/channelEntity/index.d'

defineOptions({
  name: 'AddAddress'
})

const visible = defineModel({ default: false })

const country = inject('country') as DictItemEntity[]

const props = defineProps<{
  editRecord?: SystemChannelWarehouseModel | null
  systemChannelId?: string
}>()

const emit = defineEmits<{
  success: []
}>()

const formRef = ref()
const loading = ref(false)

// 表单数据 - 只保留必要的字段
const formData = ref<any>({
  id: undefined,
  systemChannelId: props.systemChannelId,
  addressName: '', // 地址名称
  warehouseName: '', // 仓库
  contacts: '', // 发件人
  contactsPhone: '', // 电话
  contactsEmail: '', // 邮箱
  companyName: '', // 公司名称
  country: '', // 国家/地区
  province: '', // 省/州
  city: '', // 城市
  postalCode: '', // 邮编
  detailedAddressOne: '', // 地址1
  detailedAddressTwo: '', // 地址2
  serviceAddressCode: '', // 服务商地址代码
  houseNumber: '' // 门牌号
})

// 表单验证规则
const rules = ref({
  addressName: [{ required: true, message: '请输入地址名称', trigger: 'blur' }],
  warehouseName: [{ required: true, message: '请选择仓库', trigger: 'change' }],
  contacts: [{ required: true, message: '请输入发件人', trigger: 'blur' }],
  contactsPhone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
  country: [{ required: true, message: '请选择国家/地区', trigger: 'change' }]
})

const isEdit = computed(() => !!props.editRecord?.id)

// 监听编辑记录变化
watch(
  () => props.editRecord,
  newRecord => {
    if (newRecord && visible.value) {
      loadEditData(newRecord.id!)
    }
  },
  { immediate: true }
)

const loadEditData = async (id: string) => {
  try {
    loading.value = true
    const res = await findInfoSystemChannelWarehouse({ id })
    if (res.result) {
      Object.assign(formData.value, res.result)
    }
  } catch (error: any) {
    ElMessage.error('加载数据失败', error)
  } finally {
    loading.value = false
  }
}

const handleOpen = () => {
  if (!isEdit.value) {
    // 新增时重置表单
    formData.value = {
      id: undefined,
      systemChannelId: props.systemChannelId,
      addressName: '',
      warehouseName: '',
      contacts: '',
      contactsPhone: '',
      contactsEmail: '',
      companyName: '',
      country: '',
      province: '',
      city: '',
      postalCode: '',
      detailedAddressOne: '',
      detailedAddressTwo: '',
      serviceAddressCode: '',
      houseNumber: ''
    }
  }
}

const handleClose = () => {
  formRef.value?.resetFields()
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirm = async () => {
  try {
    await formRef.value?.validate()
    loading.value = true

    const apiCall = isEdit.value
      ? updateSystemChannelWarehouse
      : saveSystemChannelWarehouse
    await apiCall(formData.value)

    ElMessage.success(isEdit.value ? '修改成功' : '添加成功')
    visible.value = false
    emit('success')
  } catch (error) {
    if (error !== false) {
      // 表单验证失败时error为false
      ElMessage.error(isEdit.value ? '修改失败' : '添加失败')
    }
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑发货地址' : '添加发货地址'"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      v-loading="loading"
      :model="formData"
      :rules="rules"
      ref="formRef"
      label-position="left"
      label-width="100px"
      @submit.prevent
    >
      <el-row :gutter="16" class="w-full">
        <el-col :span="12">
          <el-form-item label="地址名称" prop="addressName">
            <el-input
              v-model.trim="formData.addressName"
              clearable
              placeholder="请输入地址名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="仓库" prop="warehouseName">
            <DSelect
              v-model="formData.warehouseName"
              :options="[
                {
                  itemText: '1',
                  itemValue: '1'
                }
              ]"
              placeholder="请选择仓库"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发件人" prop="contacts">
            <el-input
              v-model.trim="formData.contacts"
              clearable
              placeholder="请输入发件人"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="电话" prop="contactsPhone">
            <el-input
              v-model.trim="formData.contactsPhone"
              clearable
              placeholder="请输入电话"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮箱" prop="contactsEmail">
            <el-input
              v-model.trim="formData.contactsEmail"
              clearable
              placeholder="请输入邮箱"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="公司名称" prop="companyName">
            <el-input
              v-model.trim="formData.companyName"
              clearable
              placeholder="请输入公司名称"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="国家/地区" prop="country">
            <DSelect
              v-model="formData.country"
              :options="country"
              placeholder="请选择国家/地区"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="省/州" prop="province">
            <el-input
              v-model.trim="formData.province"
              clearable
              placeholder="请输入省/州"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="城市" prop="city">
            <el-input
              v-model.trim="formData.city"
              clearable
              placeholder="请输入城市"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="邮编" prop="postalCode">
            <el-input
              v-model.trim="formData.postalCode"
              clearable
              placeholder="请输入邮编"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="门牌号">
            <el-input
              v-model.trim="formData.houseNumber"
              clearable
              placeholder="请输入门牌号"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务商地址代码" prop="serviceAddressCode">
            <el-input
              v-model.trim="formData.serviceAddressCode"
              clearable
              placeholder="请输入服务商地址代码"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址1" prop="detailedAddressOne">
            <el-input
              v-model.trim="formData.detailedAddressOne"
              clearable
              placeholder="请输入地址1"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="地址2" prop="detailedAddressTwo">
            <el-input
              v-model.trim="formData.detailedAddressTwo"
              clearable
              placeholder="请输入地址2"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm">
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
