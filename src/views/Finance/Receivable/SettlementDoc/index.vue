<script setup lang="ts">
import CreateEditDialog from './components/CreateEditDialog.vue'
// import BatchSettlementDialog from './components/BatchSettlementDialog.vue'
import EditCostByCustomer from './components/EditCostByCustomer.vue'
import EditCostByWaybillNumber from './components/EditCostByWaybillNumber.vue'
import PushWaybill from './components/PushWaybill.vue'
import EditCostBySettlementNumber from './components/EditCostBySettlementNumber.vue'
import ImportWaybill from './components/ImportWaybill.vue'
import { useTable } from '@/hooks/useTable'
import type { SettdocEntity } from '@/api/settlementDoc/types'
import {
  deleteSettdoc,
  getSettdocList,
  confirmSettdoc,
  cancelSettdoc,
  batchPayment
} from '@/api/settlementDoc'
import { useToggle } from '@/hooks/useToggle'

defineOptions({
  name: 'SettlementDoc'
})

const {
  viewEntity,
  tableState: { pageSize, currentPage, total, loading, dataList },
  tableMethods,
  formData,
  selection
} = useTable<SettdocEntity>({
  immediate: true,
  initialFormData: {
    documentNumber: undefined,
    type: '1',
    customer: undefined,
    transportServiceId: undefined,
    status: undefined,
    costTypeId: undefined,
    paymentStatus: undefined
  },
  fetchDataApi: async () => {
    const res = await getSettdocList({
      pageSize: pageSize.value,
      pageNum: currentPage.value,
      ...formData
    })
    return {
      list: res.result.records,
      total: res.result.total
    }
  },
  fetchDelApi: async (record: any) => {
    if (Array.isArray(record)) {
      const ids = record.map((item: any) => item.id?.toString())
      const res = await deleteSettdoc(ids)
      return !!res
    } else {
      const res = await deleteSettdoc([record.id])
      return !!res
    }
  }
})

const [ecvisible, handleEc] = useToggle()
const [ecwnVisible, handleecwn] = useToggle()
const [pwVisible, handlepw] = useToggle()
const [ecsnvisible, handleecsn] = useToggle()
const [iwvisible, handleiw] = useToggle()

// 当前运单号
const currentWaybillNumber = ref('')

// 处理推送运单确认事件
const handlePushWaybillConfirm = (waybillNumber: string) => {
  currentWaybillNumber.value = waybillNumber
  handleecwn() // 打开按运单号新增结算单弹窗
}

// 确认结算单
const handleConfirmSettlement = async () => {
  if (!selection.value.length) {
    ElMessage.warning('请选择要确认的结算单')
    return
  }
  try {
    const ids = selection.value.map((item: any) => item.id)
    await confirmSettdoc(ids)
    ElMessage.success('确认成功')
    tableMethods.getList()
  } catch {
    ElMessage.error('确认失败')
  }
}

// 批量付款
const handleBatchPayment = async () => {
  if (!selection.value.length) {
    ElMessage.warning('请选择要付款的结算单')
    return
  }
  try {
    const ids = selection.value.map((item: any) => item.id)
    await batchPayment(ids)
    ElMessage.success('付款成功')
    tableMethods.getList()
  } catch {
    ElMessage.error('付款失败')
  }
}

// 取消结算单
const handleCancelSettlement = async () => {
  if (!selection.value.length) {
    ElMessage.warning('请选择要取消的结算单')
    return
  }
  try {
    const ids = selection.value.map((item: any) => item.id)
    await cancelSettdoc(ids)
    ElMessage.success('取消成功')
    tableMethods.getList()
  } catch {
    ElMessage.error('取消失败')
  }
}

// const reviewVisible = ref(false)
// const handleReview = () => {
//   reviewVisible.value = true
// }
const options = [
  {
    label: '头程',
    value: '1'
  },
  {
    label: '海外仓',
    value: '2'
  },
  {
    label: '分销商城',
    value: '3'
  }
]
</script>

<template>
  <ContentWrap>
    <el-tabs
      v-model="formData.type"
      class="mb-16"
      @tab-click="tableMethods.handleQuery"
    >
      <template v-for="item of options" :key="item.value">
        <el-tab-pane :label="item.label" :name="item.value"></el-tab-pane>
      </template>
    </el-tabs>
    <el-form
      :model="formData"
      label-width="90"
      ref="formDataRef"
      @submit.prevent
    >
      <el-row :gutter="8">
        <el-col :span="6">
          <el-form-item label="单号" prop="">
            <el-input
              v-model.trim="formData.documentNumber"
              clearable
              placeholder=""
            ></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="客户" prop="">
            <el-select v-model="formData.customer" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="运输服务" prop="">
            <el-select
              v-model="formData.transportServiceId"
              clearable
              placeholder=""
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="状态" prop="">
            <el-select v-model="formData.status" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="费用类型" prop="">
            <el-select v-model="formData.costTypeId" clearable placeholder="">
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="出账状态" prop="">
            <el-select
              v-model="formData.paymentStatus"
              clearable
              placeholder=""
            >
              <el-option :value="1" label="1"></el-option>
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="时间范围" prop="" label-width="140">
            <template #label>
              <el-select v-model="formData.name" clearable placeholder="">
                <el-option value="1" label="创建时间"></el-option>
                <el-option value="2" label="确认时间"></el-option>
                <el-option value="3" label="结算时间"></el-option>
                <el-option value="4" label="出账时间"></el-option>
                <el-option value="5" label="付款时间"></el-option>
              </el-select>
            </template>
            <el-date-picker
              v-model="formData.name"
              type="daterange"
              placeholder=""
            />
          </el-form-item>
        </el-col>

        <el-col :span="6">
          <el-form-item label="" prop="">
            <div class="w-full flex justify-end">
              <el-button type="primary" @click="tableMethods.handleQuery"
                >搜索</el-button
              >
              <el-button @click="tableMethods.handleReset">重置</el-button>
            </div>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ContentWrap>

  <ContentWrap class="mt-20">
    <div class="mb-18">
      <el-dropdown>
        <el-button type="primary"
          >新增结算单
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="() => handleEc()"
              >按客户</el-dropdown-item
            >
            <el-dropdown-item @click="handlepw">按运单号</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button class="ml-8" @click="handleConfirmSettlement"
        >确认结算单</el-button
      >
      <el-button class="ml-8" @click="handleBatchPayment">批量付款</el-button>
      <el-dropdown>
        <el-button class="ml-8">
          生成账单
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="() => tableMethods.handleAdd()">
              按勾选数据生成
            </el-dropdown-item>
            <el-dropdown-item>按日期生成</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <el-button class="ml-8" @click="handleCancelSettlement"
        >取消结算单</el-button
      >
      <!-- <el-button type="primary" @click="handleReview">批量新增</el-button> -->
      <el-dropdown>
        <el-button class="ml-8">
          导入/导出
          <div class="i-ep:arrow-down ml-5"></div>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item @click="handleiw"> 导入 </el-dropdown-item>
            <el-dropdown-item>导出选中项</el-dropdown-item>
            <el-dropdown-item>导出筛选项</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <el-table
      v-loading="loading"
      :data="dataList"
      :border="true"
      class="w-full"
      @selection-change="val => tableMethods.handleSelectionChange(val)"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column label="关联单号" prop="relatedNumber" width="180" />
      <el-table-column label="客户" prop="customer" width="180" />
      <el-table-column label="运输服务" prop="transportService" width="180" />
      <el-table-column label="结算单据号" prop="documentNumber" min-width="180">
        <template #default="{ row }">
          <div
            @click="() => handleecsn()"
            class="cursor-pointer text-blue-500 hover:text-blue-700"
          >
            {{ row.documentNumber }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="费用类型" prop="costType" min-width="180" />
      <el-table-column label="单位" prop="costUnit" min-width="180" />
      <el-table-column label="单位费用" prop="unitCost" min-width="180" />
      <el-table-column label="账单收费重" prop="billCharge" min-width="180" />
      <el-table-column label="金额" prop="amount" min-width="180" />
      <el-table-column
        label="待付款金额"
        prop="pendingPaymentAmount"
        min-width="180"
      />
      <el-table-column label="币种" prop="currency" min-width="180" />
      <el-table-column label="费用描述" prop="costDesc" min-width="180" />
      <el-table-column label="内部备注" prop="remarks" min-width="180" />
      <!-- 状态（1待复核、2待生成、3待确认、4账单驳回、5待收款、6待付款、7待核销、8已核销） -->
      <el-table-column label="状态" prop="status" width="180" />
      <el-table-column label="出账状态" prop="paymentStatus" width="180" />
      <el-table-column label="创建时间" prop="createTime" width="180" />
      <el-table-column label="确认时间" prop="confirmTime" width="180" />
      <el-table-column label="结算时间" prop="settlementTime" width="180" />
      <el-table-column label="出账时间" prop="disbursementTime" width="180" />
      <el-table-column label="付款时间" prop="paymentTime" width="180" />
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <el-button
            type="primary"
            link
            @click="() => tableMethods.handleEdit(row)"
          >
            编辑
          </el-button>
          <el-button
            type="primary"
            link
            @click="() => tableMethods.handleDetail(row)"
          >
            详情
          </el-button>
          <el-button
            type="danger"
            link
            @click="() => tableMethods.hadnleDel(row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, ->, sizes, prev, pager, next, jumper"
      :total="total"
      class="mt-16"
    />
  </ContentWrap>

  <CreateEditDialog
    v-model="viewEntity.visible"
    :view-entity="viewEntity"
    @refresh="() => tableMethods.getList()"
  ></CreateEditDialog>
  <!-- 批量结算 -->
  <!-- <BatchSettlementDialog v-model="reviewVisible"></BatchSettlementDialog> -->
  <!-- 按客户新增结算单 -->
  <EditCostByCustomer
    v-model="ecvisible"
    @refresh="() => tableMethods.getList()"
  ></EditCostByCustomer>

  <!-- 推送运单(按运单号新增结算单前置弹窗) -->
  <PushWaybill
    v-model="pwVisible"
    @confirm="handlePushWaybillConfirm"
  ></PushWaybill>

  <!-- 按运单号新增结算单 -->
  <EditCostByWaybillNumber
    v-model="ecwnVisible"
    :waybill-number="currentWaybillNumber"
    @refresh="() => tableMethods.getList()"
  ></EditCostByWaybillNumber>
  <!-- 结算单据号编辑费用 -->
  <EditCostBySettlementNumber
    v-model="ecsnvisible"
  ></EditCostBySettlementNumber>
  <!-- 导入运单 -->
  <ImportWaybill v-model="iwvisible"></ImportWaybill>
</template>

<style lang="scss" scoped></style>
