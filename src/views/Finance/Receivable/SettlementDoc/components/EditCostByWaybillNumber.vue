<script setup lang="ts">
import { saveSettdoc } from '@/api/settlementDoc'
import type { SettdocEntity } from '@/api/settlementDoc/types'

defineOptions({
  name: 'EditCostByWaybillNumber'
})

const visible = defineModel({ default: false })
const emits = defineEmits(['refresh'])

const props = defineProps<{
  waybillNumber?: string
}>()

// 获取当前时间的辅助函数
const getCurrentTime = () => {
  return new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 费用表格数据
const costTableData = ref<SettdocEntity[]>([
  {
    createTime: getCurrentTime(),
    costTypeId: '',
    costUnit: '',
    unitCost: '',
    currency: '',
    billCharge: '',
    costDesc: '',
    remarks: ''
  }
])

// 运单信息表格数据
const waybillTableData = ref([
  {
    waybillNumber: '',
    customer: '',
    serviceProviderService: '',
    serviceProviderWeight: '',
    serviceProviderVolume: '',
    serviceProviderChargeWeight: '',
    transportService: '',
    actualWeight: '',
    volume: '',
    chargeWeight: '',
    itemAttribute: ''
  }
])

const handleOpen = () => {
  visible.value = true
  if (props.waybillNumber) {
    // 根据运单号获取运单信息
    waybillTableData.value[0].waybillNumber = props.waybillNumber
  }
}

const handleClose = () => {
  visible.value = false
  // 重置数据
  costTableData.value = [
    {
      createTime: getCurrentTime(),
      costTypeId: '',
      costUnit: '',
      unitCost: '',
      currency: '',
      billCharge: '',
      costDesc: '',
      remarks: ''
    }
  ]
}

const handleCancel = () => {
  visible.value = false
}

const handleConfirn = async () => {
  try {
    // 构建结算单数据
    const settlementData = costTableData.value.map(cost => ({
      ...cost,
      relatedNumber: waybillTableData.value[0].waybillNumber,
      customer: waybillTableData.value[0].customer,
      transportService: waybillTableData.value[0].transportService,
      type: '1' // 头程
    }))

    await saveSettdoc(settlementData)
    ElMessage.success('新增结算单成功')
    emits('refresh')
    visible.value = false
  } catch {
    ElMessage.error('新增结算单失败')
  }
}

// 添加费用行
const handleAddCost = () => {
  costTableData.value.push({
    createTime: getCurrentTime(),
    costTypeId: '',
    costUnit: '',
    unitCost: '',
    currency: '',
    billCharge: '',
    costDesc: '',
    remarks: ''
  })
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="编辑费用"
    width="700"
    @open="handleOpen"
    @close="handleClose"
  >
    <div>
      <el-table :data="costTableData" :border="true" style="width: 100%">
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="时间" width="160">
          <template #default="{ row }">
            <span>{{ row.createTime }}</span>
          </template>
        </el-table-column>
        <el-table-column label="费用类型" width="150">
          <template #default="{ row }">
            <el-select v-model="row.costTypeId" placeholder="请选择">
              <el-option label="运费" value="1"></el-option>
              <el-option label="燃油费" value="2"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="单位" width="120">
          <template #default="{ row }">
            <el-input v-model="row.costUnit" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="单位费用" width="120">
          <template #default="{ row }">
            <el-input v-model="row.unitCost" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="币种" width="100">
          <template #default="{ row }">
            <el-select v-model="row.currency" placeholder="请选择">
              <el-option label="CNY" value="CNY"></el-option>
              <el-option label="USD" value="USD"></el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="账单收费重" width="120">
          <template #default="{ row }">
            <el-input v-model="row.billCharge" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="费用描述" width="150">
          <template #default="{ row }">
            <el-input v-model="row.costDesc" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="内部备注" width="150">
          <template #default="{ row }">
            <el-input v-model="row.remarks" placeholder="请输入"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template #default="{ $index }">
            <el-button
              type="danger"
              link
              @click="costTableData.splice($index, 1)"
              v-if="costTableData.length > 1"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div
        border="1px solid #ebeef5"
        class="h-40 w-full flex-center color-#3489ff cursor-pointer"
        @click="handleAddCost"
      >
        <div class="i-ep:circle-plus text-20"></div>
        <div class="ml-8">添加费用</div>
      </div>

      <el-table
        :data="waybillTableData"
        :border="true"
        style="width: 100%"
        class="mt-20"
      >
        <el-table-column label="运单号" prop="waybillNumber" width="150" />
        <el-table-column label="客户" prop="customer" width="120" />
        <el-table-column
          label="服务商服务"
          prop="serviceProviderService"
          width="120"
        />
        <el-table-column
          label="服务商实重"
          prop="serviceProviderWeight"
          width="120"
        />
        <el-table-column
          label="服务商体积/材积重"
          prop="serviceProviderVolume"
          width="140"
        />
        <el-table-column
          label="服务商收费重"
          prop="serviceProviderChargeWeight"
          width="120"
        />
        <el-table-column label="运输服务" prop="transportService" width="120" />
        <el-table-column label="实重" prop="actualWeight" width="100" />
        <el-table-column label="体积/材积重" prop="volume" width="120" />
        <el-table-column label="收费重" prop="chargeWeight" width="100" />
        <el-table-column label="物品属性" prop="itemAttribute" width="120" />
      </el-table>
    </div>
    <div class="mt-16">共计1条</div>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
