<script setup lang="ts">
defineOptions({
  name: 'PushWaybill'
})

const visible = defineModel({ default: false })

const emits = defineEmits(['confirm'])

const formData = ref({
  waybillNumber: ''
})

const handleOpen = () => {
  visible.value = true
}
const handleClose = () => {
  visible.value = false
  formData.value.waybillNumber = ''
}
const handleCancel = () => {
  visible.value = false
}
const handleConfirn = () => {
  if (formData.value.waybillNumber.trim()) {
    emits('confirm', formData.value.waybillNumber)
    visible.value = false
  } else {
    ElMessage.warning('请输入运单号')
  }
}
</script>

<template>
  <el-dialog
    v-model="visible"
    title="推送运单"
    width="400"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-form
      :model="formData"
      ref="formDataRef"
      label-width="80px"
      @submit.prevent
    >
      <el-form-item label="运单号" prop="">
        <el-input
          v-model.trim="formData.waybillNumber"
          clearable
          placeholder="请输入运单号"
        ></el-input>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirn"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
