<script setup lang="ts">
import { saveSettdoc, updateSettdoc } from '@/api/settlementDoc'

defineOptions({
  name: 'CreateEditDialog'
})
const viewVisible = defineModel({ default: false })

const emits = defineEmits(['refresh'])

const { viewEntity } = defineProps<{
  viewEntity: ViewEntity
}>()

const initialFormData = () => {
  return {
    id: undefined,
    relatedNumber: undefined,
    customer: undefined,
    customerId: undefined,
    transportService: undefined,
    transportServiceId: undefined,
    documentNumber: undefined,
    costType: undefined,
    costTypeId: undefined,
    costUnit: undefined,
    costUnitId: undefined,
    unitCost: undefined,
    billCharge: undefined,
    amount: undefined,
    pendingPaymentAmount: undefined,
    currency: undefined,
    costDesc: undefined,
    remarks: undefined,
    type: '1',
    warehouse: undefined,
    warehouseId: undefined
  }
}
const formData = ref(initialFormData())
const formDataRef = ref()
const rules = ref({})

const titleMap: Record<DlViewType, string> = {
  ADD: '新增结算单',
  EDIT: '编辑结算单',
  DETAIL: '结算单详情'
}

const titleStr = computed(() => {
  return titleMap[viewEntity.type] as string
})

const apiMap: Record<DlViewType, (prop?: any) => Promise<any>> = {
  ADD: saveSettdoc,
  EDIT: updateSettdoc,
  DETAIL: () => Promise.resolve({})
}

// const disabled = computed(() => viewEntity.type === 'DETAIL')

const handleOpen = () => {
  if (['EDIT', 'DETAIL'].includes(viewEntity.type)) {
    formData.value = Object.assign(formData.value, viewEntity.record)
  }
}
const handleClose = () => {
  viewVisible.value = false
  Object.assign(formData.value, initialFormData())
}
const handleCancel = () => {
  viewVisible.value = false
}
const handleConfirm = () => {
  formDataRef.value.validate(async (valid: any) => {
    if (valid) {
      try {
        // API 需要数组格式
        const dataArray = [formData.value]
        await apiMap[viewEntity.type](dataArray)
        ElMessage.success(viewEntity.type === 'ADD' ? '新增成功' : '编辑成功')
        emits('refresh')
        handleCancel()
      } catch {
        ElMessage.error(viewEntity.type === 'ADD' ? '新增失败' : '编辑失败')
      }
    }
  })
}
</script>

<template>
  <el-dialog
    v-model="viewVisible"
    :title="titleStr"
    width="700"
    class="custom-dialog"
    @open="handleOpen"
    @close="handleClose"
  >
    <el-scrollbar max-height="400" class="w-full">
      <el-form
        :model="formData"
        :rules="rules"
        ref="formDataRef"
        label-position="top"
        @submit.prevent
        class="w-full"
      >
        <el-row :gutter="16" class="w-full">
          <el-col :span="12">
            <el-form-item label="关联单号" prop="relatedNumber">
              <el-input
                v-model.trim="formData.relatedNumber"
                clearable
                placeholder="请输入关联单号"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="客户" prop="customer">
              <el-input
                v-model.trim="formData.customer"
                clearable
                placeholder="请输入客户名称"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="运输服务" prop="transportService">
              <el-input
                v-model.trim="formData.transportService"
                clearable
                placeholder="请输入运输服务"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用类型" prop="costType">
              <el-input
                v-model.trim="formData.costType"
                clearable
                placeholder="请输入费用类型"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用单位" prop="costUnit">
              <el-input
                v-model.trim="formData.costUnit"
                clearable
                placeholder="请输入费用单位"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="单位费用" prop="unitCost">
              <el-input
                v-model.trim="formData.unitCost"
                clearable
                placeholder="请输入单位费用"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="账单收费重" prop="billCharge">
              <el-input
                v-model.trim="formData.billCharge"
                clearable
                placeholder="请输入账单收费重"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="金额" prop="amount">
              <el-input
                v-model.trim="formData.amount"
                clearable
                placeholder="请输入金额"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="币种" prop="currency">
              <el-select
                v-model="formData.currency"
                clearable
                placeholder="请选择币种"
              >
                <el-option label="CNY" value="CNY"></el-option>
                <el-option label="USD" value="USD"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="费用描述" prop="costDesc">
              <el-input
                v-model.trim="formData.costDesc"
                clearable
                placeholder="请输入费用描述"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内部备注" prop="remarks">
              <el-input
                v-model.trim="formData.remarks"
                clearable
                placeholder="请输入内部备注"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss" scoped></style>
