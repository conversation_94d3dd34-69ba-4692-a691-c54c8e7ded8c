export interface SettdocEntity {
  id?: string
  createTime?: string
  createBy?: string
  updateTime?: string
  updateBy?: string
  status?: string // 状态（confirm 待确认、confirmed 已确认、waitpay待付款、paid已付款、debt欠款）
  delStatus?: number // 删除状态（1正常，0删除）
  relatedNumber?: string // 关联订单
  customer?: string // 客户名称
  customerId?: string // 客户id
  transportService?: string // 运输服务
  transportServiceId?: string // 运输服务id
  documentNumber?: string // 结算单据号
  costType?: string // 费用类型名称
  costTypeId?: string // 费用类型id
  costUnit?: string // 费用单位
  costUnitId?: string // 费用单位id
  unitCost?: string // 单位费用
  billCharge?: string // 账单收费重
  amount?: string // 金额
  pendingPaymentAmount?: string // 待付款金额
  currency?: string // 币种
  costDesc?: string // 费用描述
  remarks?: string // 内部备注
  paymentStatus?: string // 出账状态（noDisbursement未出账、disbursement已出账）
  confirmTime?: string // 确认时间
  settlementTime?: string // 结算时间
  disbursementTime?: string // 出账时间
  paymentTime?: string // 付款时间
  type?: string // 1头程、2海外仓、3分销商仓库
  warehouse?: string // 仓库
  warehouseId?: string // 仓库id
  freezeAmount?: string // 冻结金额
  adAmount?: string // 实扣金额
}

export interface FilterParams extends SettdocEntity {
  pageSize: number
  pageNum: number
  order?: string // 排序
  customerId?: string[] // 客户id数组
  timeType?: number // 时间类型（1、创建时间 2、确认时间 3、结算时间 4、出账时间 5、付款时间）
  startTime?: string // 开始时间
  endTime?: string // 结束时间
  ids?: string[] // id数组
  shippingService?: string // 运输服务
}
