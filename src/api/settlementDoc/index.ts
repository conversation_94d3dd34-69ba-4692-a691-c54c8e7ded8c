import request from '@/axios'
import type { FilterParams, SettdocEntity } from './types'

/**
 * @description: 分页获取结算单管理管理列表
 * @param {FilterParams} data
 * @return {*}
 */
export const getSettdocList = (
  data: FilterParams
): Promise<IResponse<BasePage<SettdocEntity[]>>> => {
  return request.post({
    url: '/api/management/financeSettdocManage/getList',
    data
  })
}

/**
 * @description: 新增结算单
 * @param {SettdocEntity[]} financeSettdocManageModels
 * @return {*}
 */
export const saveSettdoc = (
  financeSettdocManageModels: SettdocEntity[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeSettdocManage/save',
    data: {
      financeSettdocManageModels
    }
  })
}

/**
 * @description: 修改结算单
 * @param {SettdocEntity[]} financeSettdocManageModels
 * @return {*}
 */
export const updateSettdoc = (
  financeSettdocManageModels: SettdocEntity[]
): Promise<IResponse<any>> => {
  return request.post({
    url: '/api/management/financeSettdocManage/update',
    data: {
      financeSettdocManageModels
    }
  })
}

/**
 * @description: 根据id获取结算单信息
 * @param {string} id
 * @return {*}
 */
export const getSettdocById = (
  id: string
): Promise<IResponse<SettdocEntity>> => {
  return request.get({
    url: `/api/management/financeSettdocManage/getById/${id}`
  })
}

/**
 * @description: 结算单确认
 * @param {string} ids
 * @return {*}
 */
export const confirmSettdoc = (ids: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeSettdocManage/confirm',
    params: {
      ids
    }
  })
}

/**
 * @description: 取消结算单
 * @param {string} ids
 * @return {*}
 */
export const cancelSettdoc = (ids: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeSettdocManage/cancel',
    params: {
      ids
    }
  })
}

/**
 * @description: 批量支付
 * @param {string} ids
 * @return {*}
 */
export const batchPayment = (ids: string[]): Promise<IResponse<any>> => {
  return request.get({
    url: '/api/management/financeSettdocManage/batchPayment',
    params: {
      ids
    }
  })
}
