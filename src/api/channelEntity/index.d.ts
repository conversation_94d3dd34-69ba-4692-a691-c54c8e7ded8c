/**
 * 系统渠道绑定仓库地址查询参数类型定义
 * 所有字段均为可选，用于条件筛选
 */
export interface SystemChannelWarehouseModel {
  /**
   * 每页条数
   */
  pageSize?: number // integer(int32)

  /**
   * 页码
   */
  pageNum?: number // integer(int32)

  /**
   * 排序方式
   */
  order?: string

  /**
   * 唯一标识ID
   */
  id?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string // date-time格式

  /**
   * 修改时间
   */
  updateTime?: string // date-time格式

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 系统渠道id
   */
  systemChannelId?: string

  /**
   * 仓库id
   */
  warehouseId?: string

  /**
   * 地址名称
   */
  addressName?: string

  /**
   * 仓库名称
   */
  warehouseName?: string

  /**
   * 仓库代码
   */
  warehouseCode?: string

  /**
   * 仓库类型
   */
  warehouseType?: string

  /**
   * 联系人
   */
  contacts?: string

  /**
   * 联系人电话
   */
  contactsPhone?: string

  /**
   * 邮箱
   */
  contactsEmail?: string

  /**
   * 公司名称
   */
  companyName?: string

  /**
   * 邮政编码
   */
  postalCode?: string

  /**
   * 国家
   */
  country?: string

  /**
   * 国家代码
   */
  countryCode?: string

  /**
   * 省/州
   */
  province?: string

  /**
   * 省/州代码
   */
  provinceCode?: string

  /**
   * 市
   */
  city?: string

  /**
   * 市代码
   */
  cityCode?: string

  /**
   * 详细地址1
   */
  detailedAddressOne?: string

  /**
   * 详细地址2
   */
  detailedAddressTwo?: string

  /**
   * 详细地址3
   */
  detailedAddressThree?: string

  /**
   * 门牌号
   */
  houseNumber?: string

  /**
   * 服务商地址编码
   */
  serviceAddressCode?: string
}

/**
 * 系统渠道绑定仓库地址信息类型定义
 * 所有字段均为可选
 */
export interface SystemChannelWarehouseVo {
  /**
   * ID标识
   */
  id?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string

  /**
   * 修改时间
   */
  updateTime?: string

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 系统渠道id
   */
  systemChannelId?: string

  /**
   * 仓库id
   */
  warehouseId?: string

  /**
   * 地址名称
   */
  addressName?: string

  /**
   * 仓库名称
   */
  warehouseName?: string

  /**
   * 仓库代码
   */
  warehouseCode?: string

  /**
   * 仓库类型
   */
  warehouseType?: string

  /**
   * 联系人
   */
  contacts?: string

  /**
   * 联系人电话
   */
  contactsPhone?: string

  /**
   * 邮箱
   */
  contactsEmail?: string

  /**
   * 公司名称
   */
  companyName?: string

  /**
   * 邮政编码
   */
  postalCode?: string

  /**
   * 国家
   */
  country?: string

  /**
   * 国家代码
   */
  countryCode?: string

  /**
   * 省/州
   */
  province?: string

  /**
   * 省/州代码
   */
  provinceCode?: string

  /**
   * 市
   */
  city?: string

  /**
   * 市代码
   */
  cityCode?: string

  /**
   * 详细地址1
   */
  detailedAddressOne?: string

  /**
   * 详细地址2
   */
  detailedAddressTwo?: string

  /**
   * 详细地址3
   */
  detailedAddressThree?: string

  /**
   * 门牌号
   */
  houseNumber?: string

  /**
   * 服务商地址编码
   */
  serviceAddressCode?: string
}

/**
 * 系统渠道条件查询参数类型定义
 * 所有字段均为可选，用于条件筛选
 */
export interface SystemChannelConditionModel {
  /**
   * 每页条数
   */
  pageSize?: number // integer(int32)

  /**
   * 页码
   */
  pageNum?: number // integer(int32)

  /**
   * 排序方式
   */
  order?: string

  /**
   * 唯一标识ID
   */
  id?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string // date-time格式

  /**
   * 修改时间
   */
  updateTime?: string // date-time格式

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 条件
   */
  condition?: string

  /**
   * 运算
   */
  calculation?: string

  /**
   * 值
   */
  value?: string

  /**
   * 单位
   */
  unit?: string

  /**
   * 且或（and/or）
   */
  logic?: string

  /**
   * 系统渠道id
   */
  systemChannelId?: string
}

/**
 * 系统渠道条件信息类型定义
 * 所有字段均为可选
 */
export interface SystemChannelConditionVo {
  /**
   * ID标识
   */
  id?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string

  /**
   * 修改时间
   */
  updateTime?: string

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 条件
   */
  condition?: string

  /**
   * 运算
   */
  calculation?: string

  /**
   * 值
   */
  value?: string

  /**
   * 单位
   */
  unit?: string

  /**
   * 且或（and/or）
   */
  logic?: string

  /**
   * 系统渠道id
   */
  systemChannelId?: string
}

// ==================== 系统渠道绑定仓库管理 ====================

/**
 * 系统渠道绑定仓库查询参数类型定义
 * 所有字段均为可选，用于条件筛选
 */
export interface SystemChannelBindWarehouseModel {
  /**
   * 每页条数
   */
  pageSize?: number // integer(int32)

  /**
   * 页码
   */
  pageNum?: number // integer(int32)

  /**
   * 排序方式
   */
  order?: string

  /**
   * 唯一标识ID
   */
  id?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string // date-time格式

  /**
   * 修改时间
   */
  updateTime?: string // date-time格式

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 系统渠道id
   */
  systemChannelId?: string

  /**
   * 仓库id
   */
  warehouseId?: string
}

/**
 * 系统渠道绑定仓库信息类型定义
 * 所有字段均为可选
 */
export interface SystemChannelBindWarehouseVo {
  /**
   * ID标识
   */
  id?: string

  /**
   * 状态
   */
  status?: string

  /**
   * 创建时间
   */
  createTime?: string

  /**
   * 修改时间
   */
  updateTime?: string

  /**
   * 创建人
   */
  createUser?: string

  /**
   * 修改人
   */
  updateUser?: string

  /**
   * 系统渠道id
   */
  systemChannelId?: string

  /**
   * 仓库id
   */
  warehouseId?: string
}
